{"version": 3, "file": "main.js", "mappings": "qxBAAA,MAAMA,EAAOC,EAAQ,MACfC,EAAWD,EAAQ,MAEnBE,EAASC,IACb,IAAIC,EAAMD,EAAQE,QAAQ,IAAK,IAEZ,IAAfD,EAAIE,SACNF,EAAO,GAAEA,EAAI,KAAKA,EAAI,KAAKA,EAAI,KAAKA,EAAI,KAAKA,EAAI,KAAKA,EAAI,MAO5D,MAAQ,GAJEG,SAASH,EAAII,UAAU,EAAG,GAAI,QAC9BD,SAASH,EAAII,UAAU,EAAG,GAAI,QAC9BD,SAASH,EAAII,UAAU,EAAG,GAAI,OAOpCC,EAASV,EAFA,EAAH,GAAKW,QAAST,EAASU,KAAQV,GAA/B,IAAyCW,KAAMX,EAASY,QAExC,CAC1B,YACA,WACA,WACA,WACA,aAyCFC,EAAOC,QAAU,CACfC,oBAvCF,WACE,OAAOC,OAAOC,YACZD,OAAOE,QAAQV,GACZW,KAAI,EAAEC,EAAKC,KACW,iBAAVA,EACF,CAAC,CAAE,YAAWD,IAAOC,IAGvBL,OAAOE,QAAQG,GAAOF,KAAI,EAAEG,EAAOC,KACjC,CAAE,YAAWH,KAAOE,IAASrB,EAAOsB,QAG9CC,KAAK,KA4BVC,uBAxBF,WACE,OAAOT,OAAOC,YACZD,OAAOE,QAAQV,GAAQW,KAAI,EAAEC,EAAKC,KACX,iBAAVA,EACF,CAAE,GAAED,IAAOC,GAGb,CACLD,EACAJ,OAAOC,YACLD,OAAOE,QAAQG,GAAOF,KAAI,EAAEG,EAAOI,KAC1B,CACJ,GAAEJ,IACF,qBAAoBF,KAAOE,gC,0CCnD1CK,GAAG,6BAA6B,KAC9BC,QAAOH,EAAAA,EAAAA,2BAA0BI,QAC/BD,OAAOE,iBAAiB,CACtBC,QAAS,eACTC,QAAS,UACTC,YAAa,cACbC,MAAO,OACPC,MAAO,OACP1B,QAAS,CACP,IAAK,iDACL,IAAK,iDACL,IAAK,iDACL,IAAK,iDACL,GAAI,gDACJ,IAAK,iDACL,IAAK,iDACL,IAAK,iDACL,IAAK,iDACL,IAAK,wDAMb,MAAM2B,EAAO,CACXC,UAAW,CACT,IAAK,mDACL,IAAK,mDACL,IAAK,mDACL,IAAK,mDACL,GAAI,kDACJ,IAAK,mDACL,IAAK,mDACL,IAAK,mDACL,IAAK,mDACL,IAAK,oDAGPC,SAAU,CACR,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,GAAI,iDACJ,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,IAAK,mDAGPC,SAAU,CACR,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,GAAI,iDACJ,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,IAAK,mDAGPC,SAAU,CACR,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,GAAI,iDACJ,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,IAAK,mDAGPC,SAAU,CACR,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,GAAI,iDACJ,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,IAAK,kDACL,IAAK,oDAITC,SAASC,KAAK3B,OAAO4B,KAAKR,GAA1BM,CACG,uDACDtB,IACEO,GAAI,sBAAqBP,aAAe,KACtCQ,QAAOH,EAAAA,EAAAA,2BAA0BI,QAC/BD,OAAOiB,IAAIf,iBAAiB,CAAE,CAACV,GAAMgB,EAAKhB,YAMlDO,GAAG,gCAAgC,KACjCC,QAAOb,EAAAA,EAAAA,wBAAuBc,QAC5BD,OAAOE,iBAAiB,CACtB,sBAAuB,gBACvB,uBAAwB,gBACxB,uBAAwB,gBACxB,uBAAwB,gBACxB,uBAAwB,eACxB,uBAAwB,eACxB,uBAAwB,cACxB,uBAAwB,cACxB,uBAAwB,aACxB,uBAAwB,sB", "sources": ["webpack://laravel/nova/./generators.js", "webpack://laravel/nova/./resources/js/__tests__/util/tailwindConfig.test.js"], "sourcesContent": ["const omit = require('lodash/omit')\nconst twColors = require('tailwindcss/colors')\n\nconst toRgba = hexCode => {\n  let hex = hexCode.replace('#', '')\n\n  if (hex.length === 3) {\n    hex = `${hex[0]}${hex[0]}${hex[1]}${hex[1]}${hex[2]}${hex[2]}`\n  }\n\n  const r = parseInt(hex.substring(0, 2), 16)\n  const g = parseInt(hex.substring(2, 4), 16)\n  const b = parseInt(hex.substring(4, 6), 16)\n\n  return `${r}, ${g}, ${b}`\n}\n\nconst colors = { primary: twColors.sky, ...twColors, gray: twColors.slate }\n\nconst except = omit(colors, [\n  'lightBlue',\n  'warmGray',\n  'trueGray',\n  'coolGray',\n  'blueGray',\n])\n\nfunction generateRootCSSVars() {\n  return Object.fromEntries(\n    Object.entries(except)\n      .map(([key, value]) => {\n        if (typeof value === 'string') {\n          return [[`--colors-${key}`, value]]\n        }\n\n        return Object.entries(value).map(([shade, color]) => {\n          return [`--colors-${key}-${shade}`, toRgba(color)]\n        })\n      })\n      .flat(1)\n  )\n}\n\nfunction generateTailwindColors() {\n  return Object.fromEntries(\n    Object.entries(except).map(([key, value]) => {\n      if (typeof value === 'string') {\n        return [`${key}`, value]\n      }\n\n      return [\n        key,\n        Object.fromEntries(\n          Object.entries(value).map(([shade, poop]) => {\n            return [\n              `${shade}`,\n              `rgba(var(--colors-${key}-${shade}), <alpha-value>)`,\n            ]\n          })\n        ),\n      ]\n    })\n  )\n}\n\nmodule.exports = {\n  generateRootCSSVars,\n  generateTailwindColors,\n}\n", "import {\n  generateRootCSSVars,\n  generateTailwindColors,\n} from '../../../../generators'\n\nit('generates Tailwind colors', () => {\n  expect(generateTailwindColors()).toEqual(\n    expect.objectContaining({\n      current: 'currentColor',\n      inherit: 'inherit',\n      transparent: 'transparent',\n      black: '#000',\n      white: '#fff',\n      primary: {\n        100: 'rgba(var(--colors-primary-100), <alpha-value>)',\n        200: 'rgba(var(--colors-primary-200), <alpha-value>)',\n        300: 'rgba(var(--colors-primary-300), <alpha-value>)',\n        400: 'rgba(var(--colors-primary-400), <alpha-value>)',\n        50: 'rgba(var(--colors-primary-50), <alpha-value>)',\n        500: 'rgba(var(--colors-primary-500), <alpha-value>)',\n        600: 'rgba(var(--colors-primary-600), <alpha-value>)',\n        700: 'rgba(var(--colors-primary-700), <alpha-value>)',\n        800: 'rgba(var(--colors-primary-800), <alpha-value>)',\n        900: 'rgba(var(--colors-primary-900), <alpha-value>)',\n      },\n    })\n  )\n})\n\nconst data = {\n  lightBlue: {\n    100: 'rgba(var(--colors-lightBlue-100), <alpha-value>)',\n    200: 'rgba(var(--colors-lightBlue-200), <alpha-value>)',\n    300: 'rgba(var(--colors-lightBlue-300), <alpha-value>)',\n    400: 'rgba(var(--colors-lightBlue-400), <alpha-value>)',\n    50: 'rgba(var(--colors-lightBlue-50), <alpha-value>)',\n    500: 'rgba(var(--colors-lightBlue-500), <alpha-value>)',\n    600: 'rgba(var(--colors-lightBlue-600), <alpha-value>)',\n    700: 'rgba(var(--colors-lightBlue-700), <alpha-value>)',\n    800: 'rgba(var(--colors-lightBlue-800), <alpha-value>)',\n    900: 'rgba(var(--colors-lightBlue-900), <alpha-value>)',\n  },\n\n  warmGray: {\n    100: 'rgba(var(--colors-warmGray-100), <alpha-value>)',\n    200: 'rgba(var(--colors-warmGray-200), <alpha-value>)',\n    300: 'rgba(var(--colors-warmGray-300), <alpha-value>)',\n    400: 'rgba(var(--colors-warmGray-400), <alpha-value>)',\n    50: 'rgba(var(--colors-warmGray-50), <alpha-value>)',\n    500: 'rgba(var(--colors-warmGray-500), <alpha-value>)',\n    600: 'rgba(var(--colors-warmGray-600), <alpha-value>)',\n    700: 'rgba(var(--colors-warmGray-700), <alpha-value>)',\n    800: 'rgba(var(--colors-warmGray-800), <alpha-value>)',\n    900: 'rgba(var(--colors-warmGray-900), <alpha-value>)',\n  },\n\n  trueGray: {\n    100: 'rgba(var(--colors-trueGray-100), <alpha-value>)',\n    200: 'rgba(var(--colors-trueGray-200), <alpha-value>)',\n    300: 'rgba(var(--colors-trueGray-300), <alpha-value>)',\n    400: 'rgba(var(--colors-trueGray-400), <alpha-value>)',\n    50: 'rgba(var(--colors-trueGray-50), <alpha-value>)',\n    500: 'rgba(var(--colors-trueGray-500), <alpha-value>)',\n    600: 'rgba(var(--colors-trueGray-600), <alpha-value>)',\n    700: 'rgba(var(--colors-trueGray-700), <alpha-value>)',\n    800: 'rgba(var(--colors-trueGray-800), <alpha-value>)',\n    900: 'rgba(var(--colors-trueGray-900), <alpha-value>)',\n  },\n\n  coolGray: {\n    100: 'rgba(var(--colors-coolGray-100), <alpha-value>)',\n    200: 'rgba(var(--colors-coolGray-200), <alpha-value>)',\n    300: 'rgba(var(--colors-coolGray-300), <alpha-value>)',\n    400: 'rgba(var(--colors-coolGray-400), <alpha-value>)',\n    50: 'rgba(var(--colors-coolGray-50), <alpha-value>)',\n    500: 'rgba(var(--colors-coolGray-500), <alpha-value>)',\n    600: 'rgba(var(--colors-coolGray-600), <alpha-value>)',\n    700: 'rgba(var(--colors-coolGray-700), <alpha-value>)',\n    800: 'rgba(var(--colors-coolGray-800), <alpha-value>)',\n    900: 'rgba(var(--colors-coolGray-900), <alpha-value>)',\n  },\n\n  blueGray: {\n    100: 'rgba(var(--colors-blueGray-100), <alpha-value>)',\n    200: 'rgba(var(--colors-blueGray-200), <alpha-value>)',\n    300: 'rgba(var(--colors-blueGray-300), <alpha-value>)',\n    400: 'rgba(var(--colors-blueGray-400), <alpha-value>)',\n    50: 'rgba(var(--colors-blueGray-50), <alpha-value>)',\n    500: 'rgba(var(--colors-blueGray-500), <alpha-value>)',\n    600: 'rgba(var(--colors-blueGray-600), <alpha-value>)',\n    700: 'rgba(var(--colors-blueGray-700), <alpha-value>)',\n    800: 'rgba(var(--colors-blueGray-800), <alpha-value>)',\n    900: 'rgba(var(--colors-blueGray-900), <alpha-value>)',\n  },\n}\n\ndescribe.each(Object.keys(data))(\n  `It does not generate the deprecated Tailwind colors`,\n  key => {\n    it(`does not generate \"${key}\" colors`, () => {\n      expect(generateTailwindColors()).toEqual(\n        expect.not.objectContaining({ [key]: data[key] })\n      )\n    })\n  }\n)\n\nit('generates root CSS variables', () => {\n  expect(generateRootCSSVars()).toEqual(\n    expect.objectContaining({\n      '--colors-primary-50': '240, 249, 255',\n      '--colors-primary-100': '224, 242, 254',\n      '--colors-primary-200': '186, 230, 253',\n      '--colors-primary-300': '125, 211, 252',\n      '--colors-primary-400': '56, 189, 248',\n      '--colors-primary-500': '14, 165, 233',\n      '--colors-primary-600': '2, 132, 199',\n      '--colors-primary-700': '3, 105, 161',\n      '--colors-primary-800': '7, 89, 133',\n      '--colors-primary-900': '12, 74, 110',\n    })\n  )\n\n  //   expect(generateRootCSSVars()).toEqual(\n  //     expect.not.objectContaining({\n  //       '--colors-inherit': 'inherit',\n  //       '--colors-current': 'current',\n  //       '--colors-transparent': 'transparent',\n  //     })\n  //   )\n})\n"], "names": ["omit", "require", "twColors", "toRgba", "hexCode", "hex", "replace", "length", "parseInt", "substring", "except", "primary", "sky", "gray", "slate", "module", "exports", "generateRootCSSVars", "Object", "fromEntries", "entries", "map", "key", "value", "shade", "color", "flat", "generateTailwindColors", "poop", "it", "expect", "toEqual", "objectContaining", "current", "inherit", "transparent", "black", "white", "data", "lightBlue", "warmGray", "<PERSON><PERSON><PERSON>", "coolGray", "blueGray", "describe", "each", "keys", "not"], "sourceRoot": ""}