[{"name": "vlucas/phpdotenv", "version": "v2.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "8abb4f9aa89ddea9d52112c65bbe8d0125e2fa8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/8abb4f9aa89ddea9d52112c65bbe8d0125e2fa8e", "reference": "8abb4f9aa89ddea9d52112c65bbe8d0125e2fa8e", "shasum": "", "mirrors": [{"url": "https://dl.laravel-china.org/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.0"}, "time": "2018-07-29T20:33:41+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.vancelucas.com"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"]}, {"name": "workerman/channel", "version": "v1.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/walkor/Channel.git", "reference": "1425baf00f6342bfee89b726326225a8b6a8ea80"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/Channel/zipball/1425baf00f6342bfee89b726326225a8b6a8ea80", "reference": "1425baf00f6342bfee89b726326225a8b6a8ea80", "shasum": "", "mirrors": [{"url": "https://dl.laravel-china.org/%package%/%reference%.%type%", "preferred": true}]}, "require": {"workerman/workerman": ">=3.3.0"}, "time": "2016-04-07T07:26:43+00:00", "type": "project", "installation-source": "dist", "autoload": {"psr-4": {"Channel\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "homepage": "http://www.workerman.net"}, {"name": "workerman/phpsocket.io", "version": "v1.1.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/walkor/phpsocket.io.git", "reference": "60656796076b90f9529f1859f91134a88d52dc75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/phpsocket.io/zipball/60656796076b90f9529f1859f91134a88d52dc75", "reference": "60656796076b90f9529f1859f91134a88d52dc75", "shasum": "", "mirrors": [{"url": "https://dl.laravel-china.org/%package%/%reference%.%type%", "preferred": true}]}, "require": {"workerman/channel": ">=1.0.0", "workerman/workerman": ">=3.1.8"}, "time": "2017-07-22T08:10:18+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PHPSocketIO\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "homepage": "http://www.workerman.net", "keywords": ["Socket.io"]}, {"name": "workerman/workerman", "version": "v3.5.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/walkor/Workerman.git", "reference": "f5369137eacc25e33bb0b6c8008b96e292cf670f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/Workerman/zipball/f5369137eacc25e33bb0b6c8008b96e292cf670f", "reference": "f5369137eacc25e33bb0b6c8008b96e292cf670f", "shasum": "", "mirrors": [{"url": "https://dl.laravel-china.org/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-pcntl": "*", "ext-posix": "*", "php": ">=5.3"}, "suggest": {"ext-event": "For better performance. "}, "time": "2017-12-04T06:09:14+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Workerman\\": "./"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "description": "An asynchronous event driven PHP framework for easily building fast, scalable network applications.", "homepage": "http://www.workerman.net", "keywords": ["asynchronous", "event-loop"]}]