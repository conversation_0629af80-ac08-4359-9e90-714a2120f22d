@echo off
echo =====================================
echo Redis Connection Monitor - %date% %time%
echo =====================================

echo.
echo [1] Checking Redis pool status...
php artisan redis:pool status

echo.
echo [2] Checking for expired connections...
for /f "tokens=*" %%a in ('php artisan redis:pool status ^| findstr "过期连接数"') do (
    echo %%a | findstr /C:"过期连接数: 0" >nul
    if errorlevel 1 (
        echo WARNING: Found expired connections! Cleaning...
        php artisan redis:pool clean
    ) else (
        echo OK: No expired connections found.
    )
)

echo.
echo [3] Current system time: %date% %time%
echo =====================================
echo Monitor completed.
pause 