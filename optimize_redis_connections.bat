@echo off
chcp 65001 >nul
echo.
echo ===============================================
echo           Redis连接数深度优化工具
echo ===============================================
echo.

:MENU
echo 请选择操作:
echo.
echo [1] 查看连接状态 (redis:optimize status)
echo [2] 测试统一Redis服务 (redis:optimize test)  
echo [3] 重启服务应用优化 (redis:optimize restart)
echo [4] 查看连接池状态 (redis:pool status)
echo [5] 清理连接池 (redis:pool clean)
echo [6] 一键完整优化 (推荐)
echo [0] 退出
echo.
set /p choice="请输入选择 (0-6): "

if "%choice%"=="1" goto STATUS
if "%choice%"=="2" goto TEST
if "%choice%"=="3" goto RESTART
if "%choice%"=="4" goto POOL_STATUS
if "%choice%"=="5" goto POOL_CLEAN
if "%choice%"=="6" goto FULL_OPTIMIZE
if "%choice%"=="0" goto EXIT
goto INVALID

:STATUS
echo.
echo 🔍 查看Redis连接状态...
echo =====================================
php artisan redis:optimize status
echo.
pause
goto MENU

:TEST
echo.
echo 🧪 测试统一Redis服务...
echo ========================
php artisan redis:optimize test
echo.
pause
goto MENU

:RESTART
echo.
echo 🔄 重启服务应用优化...
echo =====================
echo 警告: 这将重启Horizon队列服务，可能会影响正在处理的任务
set /p confirm="确认继续? (y/n): "
if /i "%confirm%"=="y" (
    php artisan redis:optimize restart
    echo.
    echo ✅ 重启完成！建议等待1-2分钟后查看效果
) else (
    echo 操作已取消
)
echo.
pause
goto MENU

:POOL_STATUS
echo.
echo 📋 查看连接池状态...
echo ===================
php artisan redis:pool status
echo.
pause
goto MENU

:POOL_CLEAN
echo.
echo 🧹 清理Redis连接池...
echo =====================
php artisan redis:pool clean
echo.
pause
goto MENU

:FULL_OPTIMIZE
echo.
echo 🚀 一键完整优化流程
echo ===================
echo.
echo 步骤1: 查看优化前状态...
php artisan redis:optimize status
echo.
echo 步骤2: 测试统一Redis服务...
php artisan redis:optimize test
echo.
echo 步骤3: 确认重启服务...
set /p confirm="确认重启Horizon服务? (y/n): "
if /i "%confirm%"=="y" (
    echo 正在重启服务...
    php artisan redis:optimize restart
    echo.
    echo 步骤4: 等待服务稳定 (10秒)...
    timeout /t 10 >nul
    echo.
    echo 步骤5: 查看优化后状态...
    php artisan redis:optimize status
    echo.
    echo 🎉 一键优化完成！
    echo.
    echo 📊 预期效果:
    echo   - 连接数应该从70+减少到45-55个
    echo   - Horizon进程从20个减少到6个
    echo   - 系统性能显著提升
    echo.
) else (
    echo 优化流程已取消
)
pause
goto MENU

:INVALID
echo.
echo ❌ 无效选择，请重新输入
echo.
pause
goto MENU

:EXIT
echo.
echo 👋 感谢使用Redis连接优化工具！
echo.
echo 💡 日常监控建议:
echo   - 每日执行: php artisan redis:optimize status
echo   - 每周执行: php artisan redis:pool clean
echo.
echo 📚 更多信息请查看: REDIS_CONNECTION_OPTIMIZATION_PLAN.md
echo.
pause
exit 