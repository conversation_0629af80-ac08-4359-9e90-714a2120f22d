<?php

namespace App\Console\Commands;

use App\Models\AccountLog;
use App\Models\CurrencyMatch;
use App\Models\UsersWallet;
use Illuminate\Console\Command;
use App\Models\Users;
use App\Models\LeverTransaction;
use Illuminate\Support\Facades\DB;

class CloseCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'close';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '爆仓';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        LeverTransaction::query()
            ->where('status', 1)
            ->chunkById(50, function ($items) {
                $items->each(function ($transaction) {
                    $this->handleClose($transaction);
                });
            });
    }

    protected function handleClose($transaction)
    {

        $legal = UsersWallet::where("user_id", $transaction->user_id )
            ->where("currency", 1) //usdt
            ->lockForUpdate()
            ->first();
        if (!is_null($legal)) {
            $totalMoney = bc_add($legal->change_balance, $transaction->caution_money, 4);
            $profit = $transaction->profits2;//盈亏总额,正数不平仓
            $diff = bc_add($totalMoney, $profit, 4);
            if ($diff <= 0 && $profit<0) {
                //强制平仓
                LeverTransaction::leverClose($transaction,$margin=true);
            }
        }
    }

    /**
     * 生成跟随订单
     *
     * @param $order
     * @param $follow
     * @return false|void
     */
    protected function generateFollowOrder($order, $follow)
    {
        try {
            $order = $order->getOriginal();
            $currency_match = CurrencyMatch::where('legal_id', $order['legal'])
                ->where('currency_id', $order['currency'])
                ->first();
            if (!$currency_match) { //指定交易对不存在
                throw new \Exception("指定交易对不存在");
            }
            if ($currency_match->open_lever != 1) { //未开通本交易对的交易功能
                throw new \Exception("未开通本交易对的交易功能");
            }

            $follow_order = $order;
            $follow_order['order_type'] = 2;
            $follow_order['follow_user_id'] = $order['user_id'];
            $follow_order['follow_order_id'] = $order['id'];
            $follow_order['user_id'] = $follow->user_id;
            $follow_order['status'] = LeverTransaction::TRANSACTION;

            if ($follow->type == 1) {//跟随类型：1固定比例跟随 2固定手数跟随
                $number = bc_mul($order['number'], $follow->number, 2);

                $number = $number <= 1 ? 1 : $number;
            } else {
                $number = $follow->number;
            }
            //算法参考：App\Http\Controllers\Api\LeverController->submit()
            $all_money = bc_mul($order['price'], $number);
            $caution_money = bc_div($all_money, $order['multiple']);   //保证金

            //计算手续费
            $lever_trade_fee_rate = bc_div($currency_match->lever_trade_fee ?? 0, 100);
            $trade_fee = bc_mul($all_money, $lever_trade_fee_rate); //手续费

            $follow_order['share'] = $number;
            $follow_order['number'] = $number;
            $follow_order['origin_caution_money'] = $caution_money;
            $follow_order['caution_money'] = $caution_money;
            $follow_order['trade_fee'] = $trade_fee;
            //追加用户的代理商关系
            $user = Users::query()->where('id', $follow->user_id)->first(['agent_path']);
            $follow_order['agent_path'] = $user->agent_path;

            DB::beginTransaction();

            $legal = UsersWallet::where("user_id", $follow->user_id)
                ->where("currency", $order['legal'])
                ->lockForUpdate()
                ->first();
            if (!$legal) {
                throw new \Exception("该用户对应钱包未找到");
            }
            $user_lever = $legal->lever_balance;

            $shoud_deduct = bc_add($caution_money, $trade_fee); //保证金+手续费
            if (bc_comp($user_lever, $shoud_deduct) < 0) {
                throw new \Exception($currency_match->legal_name . 'The balance is insufficient and cannot be less than:' . $shoud_deduct . '(handling fee:' . $trade_fee . ')');
            }
            unset($follow_order['id']);

            $lever_transaction = LeverTransaction::query()->create($follow_order);

            //扣除保证金
            $result = change_wallet_balance(
                $legal,
                3,
                -$caution_money,
                AccountLog::LEVER_TRANSACTION_DEDUCT_CAUTION,
                'Follow to buy ' . $currency_match->symbol . ' contract trading, price' . $order['price'] . ',Deduction of security deposit',
                false,
                0,
                0,
                serialize([
                    'trade_id' => $lever_transaction->id,
                    'all_money' => $all_money,
                    'multiple' => $order['multiple'],
                ])
            );
            if ($result !== true) {
                throw new \Exception('Failed to deduct margin:' . $result);
            }
            //扣除手续费
            $result = change_wallet_balance(
                $legal,
                3,
                -$trade_fee,
                AccountLog::LEVER_TRANSACTION_TRADE_FEE,
                'Follow to buy ' . $currency_match->symbol . ' Contract transactions, minus handling fees',
                false,
                0,
                0,
                serialize([
                    'trade_id' => $lever_transaction->id,
                    'all_money' => $all_money,
                    'lever_trade_fee_rate' => $lever_trade_fee_rate,
                ])
            );
            if ($result !== true) {
                throw new \Exception('Failed to deduct handling fee:' . $result);
            }
            DB::commit();

            dump("跟随订单已生成，ID：{$lever_transaction->id}");
        } catch (\Throwable $ex) {
            DB::rollBack();
            $msg = "跟随者用户id：{$follow->user_id}，跟随订单id：{$order['id']}，" . $ex->getMessage();
            $this->writeLog($msg);
            dump($msg);
            return false;
        }
    }

    //取消跟随
    protected function cancelFollow($follow_id)
    {
        Follow::where('id', $follow_id)->update([
            'status' => 2
        ]);
    }

    /**
     * 写日志
     * @param $msg
     */
    protected function writeLog($msg)
    {
        $path = base_path() . '/storage/logs/follow/';
        $filename = 'FollowCommand-' . date('Ymd') . '.log';
        file_exists($path) || @mkdir($path);
        error_log(date('Y-m-d H:i:s') . ' ' . $msg . PHP_EOL, 3, $path . $filename);
    }
}
