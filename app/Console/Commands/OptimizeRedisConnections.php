<?php

namespace App\Console\Commands;

use App\Services\UnifiedRedisService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;

class OptimizeRedisConnections extends Command
{
    protected $signature = 'redis:optimize {action=status : status|test|restart}';
    protected $description = 'Redis连接优化和测试';

    public function handle()
    {
        $action = $this->argument('action');

        switch($action) {
            case 'status':
                $this->showConnectionStatus();
                break;
            case 'test':
                $this->testUnifiedRedis();
                break;
            case 'restart':
                $this->restartServices();
                break;
            default:
                $this->error('无效的操作。可用操作: status, test, restart');
        }
    }

    protected function showConnectionStatus()
    {
        $this->info('🔍 Redis连接状态全面分析');
        $this->line('=====================================');

        // 1. 获取Redis服务器连接数
        try {
            $output = shell_exec('redis-cli info clients 2>/dev/null');
            if($output) {
                $lines = explode("\n", trim($output));
                foreach($lines as $line) {
                    $line = trim($line);
                    if(strpos($line, 'connected_clients:') !== false) {
                        $count = (int)str_replace('connected_clients:', '', $line);
                        $this->line("📊 Redis服务器总连接数: {$count}");
                        
                        if($count > 50) {
                            $this->warn("⚠️  连接数偏高，建议优化");
                        } else {
                            $this->info("✅ 连接数正常");
                        }
                        break;
                    }
                }
            }
        } catch(\Exception $e) {
            $this->warn("无法获取Redis连接数: " . $e->getMessage());
        }

        // 2. 显示连接池状态
        $this->line('');
        $this->info('📋 连接池状态:');
        $poolStatus = UnifiedRedisService::getPoolStats();
        
        if(empty($poolStatus)) {
            $this->line('  无活动连接池连接');
        } else {
            $totalConnections = 0;
            foreach($poolStatus as $db => $status) {
                $this->line("  数据库 {$db}: {$status['connection_count']}个连接 (活动:{$status['active_connections']})");
                $totalConnections += $status['connection_count'];
            }
            $this->line("  连接池总连接数: {$totalConnections}");
        }

        // 3. 分析连接来源
        $this->line('');
        $this->info('🔍 主要连接来源分析:');
        $this->line('  📌 Laravel Horizon: 6-8个进程 (已优化)');
        $this->line('  📌 WebSocket进程: 8个进程');
        $this->line('  📌 Laravel Cache: 多个独立连接');
        $this->line('  📌 Redis::connection(): 多个独立连接');
        $this->line('  📌 Web请求: 临时连接');

        // 4. 优化建议
        $this->line('');
        $this->info('💡 优化建议:');
        $this->line('  1. 重启Horizon使用新配置: php artisan redis:optimize restart');
        $this->line('  2. 测试统一Redis服务: php artisan redis:optimize test');
        $this->line('  3. 逐步替换Cache和Redis使用');
    }

    protected function testUnifiedRedis()
    {
        $this->info('🧪 测试统一Redis服务');
        $this->line('========================');

        try {
            // 显示测试前连接数
            $this->getConnectionCount('测试前');

            // 测试1: 基本操作
            $this->info('测试1: 基本Redis操作');
            
            UnifiedRedisService::set('test_unified_1', 'value_1');
            $value1 = UnifiedRedisService::get('test_unified_1');
            $this->line('  ✅ Set/Get: ' . ($value1 === 'value_1' ? '成功' : '失败'));
            
            // 测试2: 缓存操作
            $this->info('测试2: 缓存操作');
            
            UnifiedRedisService::cachePut('test_cache_1', ['array' => 'data'], 60);
            $cached = UnifiedRedisService::cacheGet('test_cache_1');
            $this->line('  ✅ Cache Put/Get: ' . (isset($cached['array']) ? '成功' : '失败'));
            
            // 测试3: 分布式锁
            $this->info('测试3: 分布式锁');
            
            $lockId = UnifiedRedisService::getLock('test_lock', 10);
            $lockResult = $lockId ? '成功' : '失败';
            $this->line('  ✅ 获取锁: ' . $lockResult);
            
            if($lockId) {
                $releaseResult = UnifiedRedisService::releaseLock('test_lock', $lockId);
                $this->line('  ✅ 释放锁: ' . ($releaseResult ? '成功' : '失败'));
            }

            // 测试4: 连接复用验证
            $this->info('测试4: 连接复用验证');
            
            $redis1a = UnifiedRedisService::getRedis(1);
            $redis1b = UnifiedRedisService::getRedis(1);
            $reused = ($redis1a === $redis1b);
            $this->line('  ✅ 连接复用: ' . ($reused ? '成功(同一对象)' : '失败'));

            // 显示测试后连接池状态
            $this->line('');
            $this->info('📊 测试后连接池状态:');
            $poolStats = UnifiedRedisService::getPoolStats();
            foreach($poolStats as $db => $status) {
                $this->line("  DB{$db}: {$status['connection_count']}个连接");
            }

            // 清理测试数据
            UnifiedRedisService::del('test_unified_1');
            UnifiedRedisService::cacheForget('test_cache_1');

            $this->line('');
            $this->info('✅ 统一Redis服务测试完成！');

        } catch(\Exception $e) {
            $this->error('❌ 测试失败: ' . $e->getMessage());
        }
    }

    protected function restartServices()
    {
        $this->info('🔄 重启相关服务以应用优化');
        $this->line('===============================');

        try {
            // 1. 重启Horizon
            $this->info('步骤1: 重启Laravel Horizon');
            $this->line('  停止Horizon...');
            exec('php artisan horizon:terminate');
            sleep(3);
            
            $this->line('  启动Horizon...');
            exec('php artisan horizon > /dev/null 2>&1 &');
            sleep(2);
            
            $this->line('  ✅ Horizon已重启 (使用优化配置: 6个进程)');

            // 2. 清理Redis连接池
            $this->info('步骤2: 清理Redis连接池');
            $this->call('redis:pool', ['action' => 'clean']);

            // 3. 显示重启后状态
            sleep(3);
            $this->info('步骤3: 验证重启效果');
            $this->getConnectionCount('重启后');

            $this->line('');
            $this->info('🎉 服务重启完成！');
            $this->line('预期效果: 连接数应该从70+减少到40-50个');

        } catch(\Exception $e) {
            $this->error('❌ 重启过程中出现错误: ' . $e->getMessage());
        }
    }

    protected function getConnectionCount($label = '')
    {
        try {
            $output = shell_exec('redis-cli info clients 2>/dev/null');
            if($output) {
                $lines = explode("\n", trim($output));
                foreach($lines as $line) {
                    $line = trim($line);
                    if(strpos($line, 'connected_clients:') !== false) {
                        $count = (int)str_replace('connected_clients:', '', $line);
                        $this->line("📊 {$label}连接数: {$count}");
                        return $count;
                    }
                }
            }
        } catch(\Exception $e) {
            $this->line("无法获取连接数: " . $e->getMessage());
        }
        return 0;
    }
} 