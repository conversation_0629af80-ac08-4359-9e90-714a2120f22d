<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\MarketHour;

class EsearchMarket implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $marketData;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($market_data)
    {
        $this->marketData = $market_data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            MarketHour::getAndSetEsearchMarket($this->marketData);
        } catch (\Exception $e) {
            // 记录错误日志
            \Log::error('EsearchMarket job failed', [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage(),
                'market_data' => $this->marketData,
                'trace' => $e->getTraceAsString()
            ]);

            // 重新抛出异常，让Laravel正确处理失败任务
            throw $e;
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception)
    {
        \Log::error('EsearchMarket job permanently failed', [
            'exception' => $exception->getMessage(),
            'market_data' => $this->marketData
        ]);
    }
}
