<?php

namespace App\Models;

use App\Models\BaseModel;
use App\Models\CurrencyMatch;
use App\Models\MarketHour;
use App\Models\MicroNumber;
use App\Models\Setting;
use App\Models\UsersWallet;

class Currency extends BaseModel
{
    protected $table = "currency";
    public $timestamps = false;
    protected $appends = ['opening'];//"to_pb_price"
    protected $hidden = ["key"];

    private $second_pwd = '000000';



    public function digitalCurrencyAddress()
    {
        return $this->hasMany(DigitalCurrencyAddress::class);
    }

    public function CurrencyOpening()
    {
        return $this->hasMany(CurrencyOpening::class);
    }
    public function CurrencyType()
    {
        return $this->belongsTo(CurrencyType::class, "currency_type");
    }
    public function quotation()
    {
        return $this->hasMany(CurrencyMatch::class, "legal_id", "id");
    }

    public function userswalletout()
    {
        return $this->hasMany(UsersWalletOut::class);
    }

    public function leverTransaction()
    {
        return $this->hasMany(LeverTransaction::class);
    }

    public function leverMultiple()
    {
        return $this->hasMany(LeverMultiple::class, "currency_id", "id");
    }

    public function match()
    {
        return $this->hasMany(CurrencyMatch::class);
    }
    public function microNumbers()
    {
        return $this->hasMany(MicroNumber::class)->orderBy("number", "asc");
    }
    public function getCreateTimeAttribute()
    {
        return date("Y-m-d H:i:s", $this->attributes["create_time"]);
    }
    public static function getNameById($currency_id)
    {
        $zeuJRlv = self::find($currency_id);
        return $zeuJRlv->name;
    }
    public static function getCnyPrice($currency_id)
    {
        $NwpvAUQ = Setting::getValueByKey("USDTRate", 7.08);
        $vIkVmCQ = Currency::where("name", "USDT")->select(["id"])->first();
        $aaubRQv = MarketHour::orderBy("id", "desc")->where("currency_id", $currency_id)->where("legal_id", $vIkVmCQ->id)->first();
        if (!empty($aaubRQv)) {
            $PieWyUJ = $aaubRQv->highest * $NwpvAUQ;
        } else {
            $PKZDitQ = Currency::where("id", $currency_id)->first();
            $PieWyUJ = $PKZDitQ->price * $NwpvAUQ;
        }
        if ($currency_id == $vIkVmCQ->id) {
            $PieWyUJ = 1 * $NwpvAUQ;
        }
        return $PieWyUJ;
    }

    public function getRmbRelationAttribute()
    {
        $hlOeRCQ = Setting::getValueByKey("USDTRate", 7.08);
        return $hlOeRCQ;
    }
    public function getOriginKeyAttribute($value)
    {
        $sZVuipJ = $this->attributes["key"] ?? "";
        return $sZVuipJ != "" ? decrypt($sZVuipJ) : "";
    }
    public function getKeyAttribute($value)
    {
        return $value == "" ?: "********";
    }

    public function getPriceAttribute($value)
    {
        return 9999;
    }


    public function getOpeningAttribute(): int
    {
        $opening = CurrencyOpening::where('currency_id', $this->id)->first();

        if (!$opening) {
            return 1; // 没有配置开盘时间，默认开盘
        }

        // 根据时区获取当前时间
        $timezone = $opening->timezone ?? 'UTC';
        try {
            $currentTime = new \DateTime('now', new \DateTimeZone($timezone));
        } catch (\Exception $e) {
            $currentTime = new \DateTime('now'); // 回退到系统时区
        }

        $time = $currentTime->format('H:i:s');
        $dayOfWeek = $currentTime->format('w'); // 0=Sunday, 1=Monday, ...

        return $this->isMarketOpen($opening, $dayOfWeek, $time);
    }

    /**
     * 检查市场是否开盘（支持多时段）
     */
    private function isMarketOpen($opening, $dayOfWeek, $time): int
    {
        $dayFields = [
            0 => ['sun_begin', 'sun_end', 'sun_begin_2', 'sun_end_2'],
            1 => ['mon_begin', 'mon_end', 'mon_begin_2', 'mon_end_2'],
            2 => ['tue_begin', 'tue_end', 'tue_begin_2', 'tue_end_2'],
            3 => ['wed_begin', 'wed_end', 'wed_begin_2', 'wed_end_2'],
            4 => ['thu_begin', 'thu_end', 'thu_begin_2', 'thu_end_2'],
            5 => ['fin_begin', 'fin_end', 'fin_begin_2', 'fin_end_2'],
            6 => ['sat_begin', 'sat_end', 'sat_begin_2', 'sat_end_2'],
        ];

        if (!isset($dayFields[$dayOfWeek])) {
            return 1; // 默认开盘
        }

        $fields = $dayFields[$dayOfWeek];

        // 检查第一个时段
        $begin1 = $opening->{$fields[0]};
        $end1 = $opening->{$fields[1]};

        if ($begin1 && $end1) {
            if ($time >= $begin1 && $time <= $end1) {
                return 1; // 在第一个时段内
            }
        }

        // 检查第二个时段（如果存在）
        $begin2 = $opening->{$fields[2]} ?? null;
        $end2 = $opening->{$fields[3]} ?? null;

        if ($begin2 && $end2) {
            if ($time >= $begin2 && $time <= $end2) {
                return 1; // 在第二个时段内
            }
        }

        // 如果只有第一个时段且没有第二个时段，使用原逻辑
        if ($begin1 && $end1 && !$begin2) {
            return ($time >= $begin1 && $time <= $end1) ? 1 : 0;
        }

        return 0; // 不在任何开盘时段内
    }
    public function setKeyAttribute($value)
    {
        if ($value != "") {
            return $this->attributes["key"] = encrypt($value);
        }
    }
}
