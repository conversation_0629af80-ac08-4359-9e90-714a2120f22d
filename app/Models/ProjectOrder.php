<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectOrder extends BaseModel
{
    protected $table = "project_order";
    use HasFactory;
     protected $appends = [
        'account'
    ];
    protected $casts = [
        'sub_time' => 'datetime',
    ];
    
    
    public function getAccountAttribute()
    {
        return $this->belongsTo(Users::class,  'user_id', 'id')->value('email');
    }
        public function getProjectNameAttribute()
    {
        return $this->belongsTo(Project::class,  'project_id', 'id')->value('project_name');
    }
        public function getStatusAttribute(): string
    {
        $status='';
        if($this->attributes['status']==1){
            $status='have in hand';
        }else if($this->attributes['status']==2){
            $status='Apply for refund';
        }else if($this->attributes['status']==3){
            $status='deposit';
        }else if($this->attributes['status']==4){
            $status='Refunded';
        }
        return $status;
    }
}
