<?php

namespace App\Services;

class RedisService
{
    protected static $redis = [];
    protected static $connectionPool = [];
    protected static $maxConnections = 5; // 每个数据库最大连接数
    protected static $connectionTimeout = 300; // 连接超时时间（秒）
    
    /**
     * 获取Redis连接实例（带连接池）
     * @param int $db
     * @return \Redis
     * @throws \Exception
     */
    public static function getInstance($db = 1) {
        // 检查并清理过期连接
        self::cleanExpiredConnections();
        
        // 如果已有可用连接，直接返回
        if(isset(self::$redis[$db]) && self::isConnectionValid(self::$redis[$db])) {
            self::updateConnectionTime($db);
            return self::$redis[$db];
        }
        
        // 检查连接池是否已满
        if(self::getConnectionCount($db) >= self::$maxConnections) {
            // 复用最老的连接
            return self::getReuseConnection($db);
        }
        
        // 创建新连接
        return self::createNewConnection($db);
    }
    
    /**
     * 创建新的Redis连接
     */
    protected static function createNewConnection($db) {
        $redis = new \Redis();
        
        try{
            if (!$redis->connect(config('database.redis.default.host'), config('database.redis.default.port'), 2)) {
                throw new \Exception("连接缓存服务器失败");
            }

            if (config('database.redis.default.password')) {
                if (!$redis->auth(config('database.redis.default.password'))) {
                    throw new \Exception("连接缓存服务器认证失败");
                }
            }

            if (!$redis->select($db)) {
                throw new \Exception("选择缓存数据库失败," . $redis->getLastError());
            }
            
            // 设置连接选项
            $redis->setOption(\Redis::OPT_READ_TIMEOUT, 10);
            $redis->setOption(\Redis::OPT_TCP_KEEPALIVE, 1);
            
            self::$redis[$db] = $redis;
            self::$connectionPool[$db][spl_object_hash($redis)] = [
                'connection' => $redis,
                'created_at' => time(),
                'last_used' => time()
            ];
            
        } catch (\Exception $e) {
            throw new \Exception('Redis连接错误: ' . $e->getMessage(), 500);
        }
        
        return $redis;
    }
    
    /**
     * 检查连接是否有效
     */
    protected static function isConnectionValid($redis) {
        try {
            return $redis && $redis->ping() === '+PONG';
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 更新连接使用时间
     */
    protected static function updateConnectionTime($db) {
        if(isset(self::$redis[$db])) {
            $hash = spl_object_hash(self::$redis[$db]);
            if(isset(self::$connectionPool[$db][$hash])) {
                self::$connectionPool[$db][$hash]['last_used'] = time();
            }
        }
    }
    
    /**
     * 获取指定数据库的连接数
     */
    protected static function getConnectionCount($db) {
        return isset(self::$connectionPool[$db]) ? count(self::$connectionPool[$db]) : 0;
    }
    
    /**
     * 复用最老的连接
     */
    protected static function getReuseConnection($db) {
        if(!isset(self::$connectionPool[$db]) || empty(self::$connectionPool[$db])) {
            return self::createNewConnection($db);
        }
        
        // 找到最老的连接
        $oldestTime = time();
        $oldestConnection = null;
        
        foreach(self::$connectionPool[$db] as $hash => $info) {
            if($info['last_used'] < $oldestTime && self::isConnectionValid($info['connection'])) {
                $oldestTime = $info['last_used'];
                $oldestConnection = $info['connection'];
            }
        }
        
        if($oldestConnection) {
            self::$redis[$db] = $oldestConnection;
            self::updateConnectionTime($db);
            return $oldestConnection;
        }
        
        return self::createNewConnection($db);
    }
    
    /**
     * 清理过期连接
     */
    protected static function cleanExpiredConnections() {
        $now = time();
        
        foreach(self::$connectionPool as $db => $connections) {
            foreach($connections as $hash => $info) {
                // 清理超时或无效的连接
                if(($now - $info['last_used'] > self::$connectionTimeout) || !self::isConnectionValid($info['connection'])) {
                    try {
                        $info['connection']->close();
                    } catch (\Exception $e) {
                        // 忽略关闭错误
                    }
                    unset(self::$connectionPool[$db][$hash]);
                    
                    // 如果这是当前使用的连接，也清理掉
                    if(isset(self::$redis[$db]) && spl_object_hash(self::$redis[$db]) === $hash) {
                        unset(self::$redis[$db]);
                    }
                }
            }
            
            // 如果整个数据库的连接都被清理了，删除数组
            if(empty(self::$connectionPool[$db])) {
                unset(self::$connectionPool[$db]);
            }
        }
    }
    
    /**
     * 手动关闭指定数据库的所有连接
     */
    public static function closeConnections($db = null) {
        if($db !== null) {
            // 关闭指定数据库连接
            if(isset(self::$connectionPool[$db])) {
                foreach(self::$connectionPool[$db] as $hash => $info) {
                    try {
                        $info['connection']->close();
                    } catch (\Exception $e) {
                        // 忽略关闭错误
                    }
                }
                unset(self::$connectionPool[$db]);
                unset(self::$redis[$db]);
            }
        } else {
            // 关闭所有连接
            foreach(self::$connectionPool as $dbNum => $connections) {
                self::closeConnections($dbNum);
            }
        }
    }
    
    /**
     * 获取连接池状态
     */
    public static function getPoolStatus() {
        $status = [];
        foreach(self::$connectionPool as $db => $connections) {
            $status[$db] = [
                'connection_count' => count($connections),
                'active_connections' => 0,
                'expired_connections' => 0
            ];
            
            $now = time();
            foreach($connections as $info) {
                if(self::isConnectionValid($info['connection'])) {
                    $status[$db]['active_connections']++;
                }
                if($now - $info['last_used'] > self::$connectionTimeout) {
                    $status[$db]['expired_connections']++;
                }
            }
        }
        return $status;
    }

    public function __clone() {
        die('do not clone me');
    }
}
