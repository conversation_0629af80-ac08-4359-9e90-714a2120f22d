<?php

namespace App\Nova\Shareoption;

use App\Nova\Filters\DealStatus;
use App\Nova\Filters\DealType;
use App\Nova\Filters\MicroResult;
use App\Nova\Filters\MicroType;
use App\Nova\Resource;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class MicroOrder extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\MicroOrder>
     */
    public static $model = \App\Models\MicroOrder::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'user.account_number','currency.name'
    ];
    /**
     * Custom priority level of the resource.
     *
     * @var int
     */
    public static $priority = 1;
    /**
     * The logical group associated with the resource.
     *
     * @var string
     */
    public static function group(){
        return __('SecondContract');
    }
    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->where('simulation', 0);

    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            Text::make(__('AccountNumber'), 'account')->readonly(),
            Text::make(__('Agreement'), 'symbol_name')->readonly(),
            Text::make(__('Type'), 'type_name')->readonly(),
            Text::make(__('TransactionStatus'), 'status_name')->readonly(),
            Text::make(__('Amount'), 'number')->readonly(),
            Text::make(__('ServiceCharge'), 'fee')->readonly(),
            Text::make(__('Result'), 'profit_result_name')->readonly(),
            Text::make(__('Profit'), 'fact_profits')->readonly(),
            Text::make(__('OpenPrice'), 'open_price')->readonly(),
            Text::make(__('EndPrice'), 'end_price')->readonly(),
            Text::make(__('OrderTime'), 'created_at')->readonly(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [
            new MicroType(),
            new DealStatus(),
            new MicroResult()
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the displayble label of the resource.
     *
     * @return string
     */
    public static function label()
    {
        return __('SecondDeal');
    }

    /**
     * Get the displayble singular label of the resource.
     *
     * @return string
     */
    public static function singularLabel()
    {
        return __('SecondDeal');
    }
}
