<?php

namespace App\Nova\Actions;

use App\Models\ChargeReq;
use App\Models\Setting;
use App\Models\Users;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use PHPMailer\PHPMailer\PHPMailer;

class ApplyTurnDown extends Action
{
    use InteractsWithQueue, Queueable;


    public $name='拒绝';

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            $user = Users::getById($model->uid);
            if($model->status<>1){
                return Action::danger('无法操作');
            }
            $notes=$fields->notes;
            //发送邮件
            //  从设置中取出值
            $username = Setting::getValueByKey('phpMailer_username', '');
            $host = Setting::getValueByKey('phpMailer_host', '');
            $password = Setting::getValueByKey('phpMailer_password', '');
            $port = Setting::getValueByKey('phpMailer_port', 465);
            $mail_from_name = Setting::getValueByKey('submail_from_name', '');
            $mail = new PHPMailer(true);
            $mail->isSMTP();
            $mail->CharSet = "utf-8";
            $mail->SMTPAuth = true;
            $mail->SMTPSecure = "ssl";
            $mail->Host = $host;
            $mail->Port = 465;//$port;
            $mail->Username = $username;
            $mail->Password = $password;//去开通的qq或163邮箱中找,这里用的不是邮箱的密码，而是开通之后的一个token
//            $mail->SMTPDebug = 2; //用于debug PHPMailer信息
            $mail->setFrom($username, $mail_from_name);//设置邮件来源  //发件人
            $mail->Subject = "Recharge failed"; //邮件标题
            $mail->MsgHTML("Your recharge has been rejected");
            $mail->addAddress($user->email);  //收件人（用户输入的邮箱）
            $mail->send();
            ChargeReq::with([])->where('id',$model->id)->update(['status'=>3,'notes'=>$notes]);
        }
        return Action::message('操作成功');
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Text::make(__('Notes'), 'notes'),
        ];
    }
}
