<?php

namespace App\Nova\Currency;

use App\Nova\Resource;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class CurrencyOpening extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\CurrencyOpening>
     */
    public static $model = \App\Models\CurrencyOpening::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];
    /**
     * The logical group associated with the resource.
     *
     * @var string
     */
    public static function group(){
        return __('BlockTrade');
    }


    /**
     * Custom priority level of the resource.
     *
     * @var int
     */
    public static $priority = 4;
    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            BelongsTo::make(__('CurrencyName'),'Currency', 'App\Nova\Currency\Currency'),

            // 时区选择
            Select::make(__('Timezone'), 'timezone')->options([
                'UTC' => 'UTC',
                'Asia/Tokyo' => 'Asia/Tokyo (JST)',
                'America/New_York' => 'America/New_York (EST)',
                'Europe/London' => 'Europe/London (GMT)',
                'Asia/Shanghai' => 'Asia/Shanghai (CST)',
            ])->default('UTC')->help('选择市场所在时区'),

            // 周一
            Text::make(__('Mon Session 1 Begin'),'mon_begin')->placeholder('09:00:00'),
            Text::make(__('Mon Session 1 End'),'mon_end')->placeholder('11:30:00'),
            Text::make(__('Mon Session 2 Begin'),'mon_begin_2')->placeholder('12:30:00')->help('第二时段（可选）'),
            Text::make(__('Mon Session 2 End'),'mon_end_2')->placeholder('15:00:00'),

            // 周二
            Text::make(__('Tue Session 1 Begin'),'tue_begin')->placeholder('09:00:00'),
            Text::make(__('Tue Session 1 End'),'tue_end')->placeholder('11:30:00'),
            Text::make(__('Tue Session 2 Begin'),'tue_begin_2')->placeholder('12:30:00')->help('第二时段（可选）'),
            Text::make(__('Tue Session 2 End'),'tue_end_2')->placeholder('15:00:00'),

            // 周三
            Text::make(__('Wed Session 1 Begin'),'wed_begin')->placeholder('09:00:00'),
            Text::make(__('Wed Session 1 End'),'wed_end')->placeholder('11:30:00'),
            Text::make(__('Wed Session 2 Begin'),'wed_begin_2')->placeholder('12:30:00')->help('第二时段（可选）'),
            Text::make(__('Wed Session 2 End'),'wed_end_2')->placeholder('15:00:00'),

            // 周四
            Text::make(__('Thu Session 1 Begin'),'thu_begin')->placeholder('09:00:00'),
            Text::make(__('Thu Session 1 End'),'thu_end')->placeholder('11:30:00'),
            Text::make(__('Thu Session 2 Begin'),'thu_begin_2')->placeholder('12:30:00')->help('第二时段（可选）'),
            Text::make(__('Thu Session 2 End'),'thu_end_2')->placeholder('15:00:00'),

            // 周五
            Text::make(__('Fri Session 1 Begin'),'fin_begin')->placeholder('09:00:00'),
            Text::make(__('Fri Session 1 End'),'fin_end')->placeholder('11:30:00'),
            Text::make(__('Fri Session 2 Begin'),'fin_begin_2')->placeholder('12:30:00')->help('第二时段（可选）'),
            Text::make(__('Fri Session 2 End'),'fin_end_2')->placeholder('15:00:00'),

            // 周六（通常休市）
            Text::make(__('Sat Session 1 Begin'),'sat_begin')->placeholder('00:00:00'),
            Text::make(__('Sat Session 1 End'),'sat_end')->placeholder('00:00:00'),
            Text::make(__('Sat Session 2 Begin'),'sat_begin_2')->placeholder('00:00:00')->help('第二时段（可选）'),
            Text::make(__('Sat Session 2 End'),'sat_end_2')->placeholder('00:00:00'),

            // 周日（通常休市）
            Text::make(__('Sun Session 1 Begin'),'sun_begin')->placeholder('00:00:00'),
            Text::make(__('Sun Session 1 End'),'sun_end')->placeholder('00:00:00'),
            Text::make(__('Sun Session 2 Begin'),'sun_begin_2')->placeholder('00:00:00')->help('第二时段（可选）'),
            Text::make(__('Sun Session 2 End'),'sun_end_2')->placeholder('00:00:00'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
    /**
     * Get the displayble label of the resource.
     *
     * @return string
     */
    public static function label()
    {
        return __('CurrencyOpening');
    }

    /**
     * Get the displayble singular label of the resource.
     *
     * @return string
     */
    public static function singularLabel()
    {
        return __('CurrencyOpening');
    }
}
