<?php

namespace App\Nova\Metrics;


use App\Models\Users;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Value;
use <PERSON><PERSON>\Nova\Nova;

class NewUsers extends Value
{


    public function name()
    {
        return __('New Users'); // TODO: Change the autogenerated stub
    }

    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        return $this->count($request, Users::with([])->where('simulation', 0));
    }

    /**
     * 获取 metric 的 URI 键。
     *
     * @return string
     */
    public function uriKey(): string
    {
        return 'new-users';
    }

    public function icon($icon): string
    {
        return 'user-add'; // TODO: Change the autogenerated stub
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            'TODAY' => Nova::__('Today'),
            30 => Nova::__('30 Days'),
            60 => Nova::__('60 Days'),
            365 => Nova::__('365 Days'),
            'MTD' => Nova::__('Month To Date'),
            'QTD' => Nova::__('Quarter To Date'),
            'YTD' => Nova::__('Year To Date'),
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }
}
