<?php

namespace App\Http\Controllers\Api;

use App\Enums\FromType;
use App\Http\Controllers\Controller;
use App\Models\LeverTransaction;
use App\Models\Users;
use App\Models\UsersWallet;
use App\Services\LogService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class IndexController extends Controller
{
    //
    public function test2()
    {

        LeverTransaction::query()
            ->where('status', 1)
            ->chunkById(50, function ($items) {
                $items->each(function ($transaction) {
                    $this->handleClose($transaction);
                });
            });
    }
    protected function handleClose($transaction)
    {

        $legal = UsersWallet::where("user_id", $transaction->user_id )
            ->where("currency", 1) //usdt
            ->lockForUpdate()
            ->first();
        if (!is_null($legal)) {
            $totalMoney = bc_add($legal->change_balance, $transaction->caution_money, 4);
            $profit = $transaction->profits2;//盈亏总额
            $diff = bc_sub($totalMoney, $profit, 4);
            if($diff<=0)
            {
                //强制平仓
                LeverTransaction::leverClose($transaction);
            }
        }



    }

}
