@echo off
echo =====================================
echo Redis Connection Pool Test
echo =====================================

echo.
echo 正在查找PHP执行文件...

rem 尝试不同的PHP路径
set PHP_PATHS="php" "C:\php\php.exe" "C:\xampp\php\php.exe"

set PHP_FOUND=0
for %%i in (%PHP_PATHS%) do (
    %%i --version >nul 2>&1
    if not errorlevel 1 (
        echo 找到PHP: %%i
        set PHP_CMD=%%i
        set PHP_FOUND=1
        goto found_php
    )
)

:found_php
if %PHP_FOUND%==0 (
    echo 错误: 无法找到PHP执行文件
    echo 请手动运行: php artisan test:redis-pool
    pause
    exit /b 1
)

echo.
echo 使用PHP: %PHP_CMD%
echo.

echo [1] 测试Redis连接池状态...
%PHP_CMD% artisan redis:pool status

echo.
echo [2] 运行Redis连接池测试...
%PHP_CMD% artisan test:redis-pool

echo.
echo [3] 再次检查连接池状态...
%PHP_CMD% artisan redis:pool status

echo.
echo 测试完成!
pause 