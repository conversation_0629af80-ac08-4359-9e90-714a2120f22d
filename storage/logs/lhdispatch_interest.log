start
end
start
end
start
end
start
end
start
end

   Illuminate\Database\QueryException 

  SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `lh_deposit_order` where (`status` = 1) and `start_at` < 2025-06-07 and `last_settle_time` < 2025-06-07 or `last_settle_time` is null limit 500)

  at vendor/laravel/framework/src/Illuminate/Database/Connection.php:801
    797▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    798▕                 );
    799▕             }
    800▕ 
  ➜ 801▕             throw new QueryException(
    802▕                 $this->getName(), $query, $this->prepareBindings($bindings), $e
    803▕             );
    804▕         }
    805▕     }

      [2m+22 vendor frames [22m

  23  app/Console/Commands/LHDisptchInterest.php:24
      Illuminate\Database\Eloquent\Builder::get()
      [2m+12 vendor frames [22m

  36  artisan:35
      Illuminate\Foundation\Console\Kernel::handle()


   Illuminate\Database\QueryException 

  SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `lh_deposit_order` where (`status` = 1) and `start_at` < 2025-06-08 and `last_settle_time` < 2025-06-08 or `last_settle_time` is null limit 500)

  at vendor/laravel/framework/src/Illuminate/Database/Connection.php:801
    797▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    798▕                 );
    799▕             }
    800▕ 
  ➜ 801▕             throw new QueryException(
    802▕                 $this->getName(), $query, $this->prepareBindings($bindings), $e
    803▕             );
    804▕         }
    805▕     }

      [2m+22 vendor frames [22m

  23  app/Console/Commands/LHDisptchInterest.php:24
      Illuminate\Database\Eloquent\Builder::get()
      [2m+12 vendor frames [22m

  36  artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

start
end
start
end
start
end
start
end
start
end
start
end
start
end
start
end
start
end
start
end
start
end
start
end
start
end
start
end
start
end
start
end
start
end
start
end
