<?php
return [
    "is_lock" => "Compte verrouillé",
    "unknown" => "Type inconnu",
    "1" => "Solde du compte de fonds de rapprochement de fond",
    "2" => "Solde verrouillé du compte de fonds de rapprochement de fond",
    "3" => "Solde du compte en monnaie de réglage de fond",
    "4" => "Compte de monnaie de rapprochement de fond",
    "5" => "Solde du compte contractuel de rapprochement de fond",
    "6" => "Compte contractuel de rapprochement des antécédents",
    "7" => "Solde du compte d'option de rapprochement de fond",
    "8" => "Compte d'option de rapprochement de fond",
    "9" => "Transfert du compte de capital du compte de transaction",
    "10" => "Transfert du compte de transaction au compte de capital",
    "11" => "Transferts de fonds",
    "12" => "Transferts de fonds",
    "13" => "Transfert du compte contractuel au compte de transaction",
    "14" => "Transfert du compte contractuel au compte de transaction",
    "15" => "Compte de transaction à compte contractuel",
    "16" => "Compte contractuel transfert du compte de transaction",
    "33" => "Invitation au remboursement de la Commission",
    "99" => "Retrait par l'utilisateur",
    "102" => "Annuler une transaction d'achat",
    "103" => "Annuler la vente",
    "65" => "Solde des opérations de transfert de fonds",
    "66" => "Transfert du solde des opérations de change",
    "67" => "Transfert du solde des opérations contractuelles",
    "206" => "Virements aux contrats et réductions de fonds",
    "207" => "Transfert de fonds au contrat, augmentation du contrat",
    "208" => "Le transfert des fonds du contrat est approuvé et le contrat est réduit.",
    "209" => "Le transfert du Fonds contractuel est approuvé et le Fonds est augmenté.",
    "210" => "Conversion contractuelle en capital, gel de la valeur de conversion contractuelle",
    "211" => "Le contrat est transféré au Fonds et n'est pas approuvé pour le dégel.",
    "107" => "Conversion de certificats en USD",
    "37" => "Conversion des certificats, augmentation de la monnaie contractuelle",
    "212" => "Libération historique des gains et pertes, augmentation de la monnaie contractuelle",
    "105" => "Certificat de récompense",
    "106" => "Récompense monnaie numérique",
    "108" => "Carte de régulation",
    "38" => "Demande de déduction de la tvus pour devenir un commerçant",
    "39" => "Conversion des actifs, réduction des fonds en monnaie de détention",
    "40" => "Échange d'actifs, augmentation du capital usdt",
    "41" => "Activer le patron",
    "42" => "Mine lock - out",
    "43" => "Transfert du solde du compte boss",
    "44" => "Retour de la serrure",
    "45" => "Defi funding record",
    "51" => "Opérations en monnaie",
    "50" => "Blocage des transactions en monnaie",
    "100" => "Retrait réussi",
    "101" => "Échec de la collecte de pièces",
    "700" => "Ieo Order",
    "701" => "Augmentation du transfert de devises",
    "702" => "Diminution du transfert de devises",
    "703" => "Frais de transfert de devises",
    "704" => "Mine lock - out",
    "705" => "Revenus de l'exploitation minière",
    "706" => "Le singulier inférieur ne peut être inférieur à",
    "1000" => "Achat de double monnaie",
    "1001" => "Règlement financier en double monnaie",
    "707" => "Suivre l'achat - déduire la marge",
    "708" => "Suivre l'achat - déduire les frais de manutention",
    "1002" => "Acheter NFT",
    "1003" => "Déduction de la marge de soumission",
    "1005" => "NFT Auction Order payment",
    "1004" => "Dépôt de remboursement des soumissions",
    '32' => "Gel d'évasion",
    '35' => "Frais de transaction",
    '30' => "Les transactions contractuelles déduisent la marge",
];