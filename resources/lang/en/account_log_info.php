<?php
return [
    "is_lock" => "Lock account",
    "unknown" => "unknown type",
    '1' => 'Adjust capital account balance',
    '2' => 'Adjust the locked balance of the capital account',
    '3' => 'Adjust the balance of the currency account',
    '4' => 'Adjust currency account',
    '5' => 'Adjust contract account balance',
    '6' => 'Adjustment contract account',
    '7' => 'Adjust option account balance',
    '8' => 'Adjust option account',
    '9' => 'Fund account is transferred out of trading account',
    '10' => 'Transaction account to capital account',
    '11' => 'Fund transfer',
    '12' => 'Fund transfer out',
    '13' => 'Transfer from contract account to trading account',
    '14' => 'Transfer from contract account to trading account',
    '15' => 'Transaction account to contract account',
    '16' => 'Contract account transfer out of trading account',
    '33' => 'Invitation to return commission',
    '99' => 'User withdrawal',
    '102' => 'Cancel purchase transaction',
    '103' => 'Cancel sell transaction',
    '65' => 'Transaction balance of funds transferred',
    '66' => 'Transaction balance of currency transaction',
    '67' => 'Contract transaction balance transfer',
    '206' => 'Transfer to contract and reduce funds',
    '207' => 'Funds are transferred to the contract, the contract is increased',
    '208' => 'Contract fund transfer approved, contract reduced',
    '209' => 'Contract fund transfer approved, capital increase',
    '210' => 'Contract transfer funds, freeze contract conversion value',
    '211' => 'Contract transfer funds, the review fails to unfreeze',
    '107' => 'Token exchange for USDT',
    '37' => 'token exchange, contract currency increase',
    '212' => 'Historical profit and loss release, increase contract currency',
    '105' => 'Reward Token',
    '106' => 'Reward digital currency',
    '108' => 'Adjust pass',
    '38' => 'Apply to become a merchant to deduct USDT',
    '39' => 'Asset exchange, reduce holding currency funds',
    '40' => 'Asset exchange, increase USDT funds',
    '41' => 'Activate BOSS',
    '42' => 'Locked Mining',
    '43' => 'Transfer the balance from the BOSS account',
    '44' => 'Lock-up return',
    '45' => 'DeFi fund record',
    '51' => 'Coin transaction',
    '50' => 'Coin transaction frozen',
    '100' => 'Withdrawal successfully',
    '101' => 'Withdrawal failed',
    '700' => 'IEO order',
    '701' => 'Coin transfer increase',
    '702' => 'Coin transfer reduction',
    '703' => 'Coin transfer fee',
    '704' => 'Locked Mining',
    '705' => 'Mining income',
    '706' => 'The number of orders cannot be lower than',
    '1000' => 'Buy dual currency wealth management',
    '1001' => 'Dual currency financial settlement',
    '707' => 'Follow the purchase - deduct margin',
    '708' => 'Follow the purchase - deduct handling fee',
    '1002' => 'Buy NFT',
    '1003' => 'The auction will deduct the deposit',
    '1005' => 'NFT auction order payment',
    '1004' => 'Bidding refund deposit',
    '32' => 'Breakout freeze',
    '35' => 'Transaction fee',
    '30' => 'Contract transactions deduct margin',
    // "1" => "Adjust the balance of the capital account",
    // "2" => "Adjust the locked balance of the fund account",
    // "3" => "Balance of back office reconciliation currency account",
    // "4" => "Back office reconciliation currency account",
    // "5" => "Back office reconciliation contract account balance",
    // "6" => "Back office reconciliation contract account",
    // "7" => "Back office adjustment option account balance",
    // "8" => "Back office reconciliation option account",
    // "9" => "Fund account transfer out transaction account",
    // "10" => "Transfer from transaction account to capital account",
    // "11" => "Fund transfer in",
    // "12" => "Fund transfer out",
    // "13" => "Transfer from contract account to trading account",
    // "14" => "Transfer from contract account to trading account",
    // "15" => "Transfer from transaction account to contract account",
    // "16" => "Contract account transfer out transaction account",
    // "33" => "Invitation to return commission",
    // "99" => "User withdrawal",
    // "102" => "Cancel purchase transaction",
    // "103" => "Cancel the sale transaction",
    // "65" => "Transfer in fund transaction balance",
    // "66" => "Currency transaction balance transfer in",
    // "67" => "Transfer in of contract transaction balance",
    // "206" => "Transfer in contracts and reduction of funds",
    // "207" => "Transfer of funds into contracts and increase of contracts",
    // "208" => "Transfer of contract funds",
    // "209" => "The transfer of contract funds was approved and the funds increased",
    // "210" => "Contract transfer to capital, freeze contract conversion value",
    // "211" => "The contract is transferred to capital, and it fails to pass the review and unfreeze",
    // "107" => "Pass exchange usdt",
    // "37" => "Token exchange, contract currency increase",
    // "212" => "Release historical profit and loss and increase contract currency",
    // "105" => "Reward pass",
    // "106" => "Reward digital currency",
    // "108" => "Regulating Tongzheng",
    // "38" => "Apply to become a merchant to deduct usdt",
    // "39" => "Asset exchange, reduce holding currency funds",
    // "40" => "Asset exchange to increase usdt funds",
    // "41" => "Activate boss",
    // "42" => "Lockdown mining",
    // "43" => "Transfer out balance from boss account",
    // "44" => "Lock return",
    // "45" => "Defi fund record",
    // "51" => "Currency transaction",
    // "50" => "Currency transaction freeze",
    // "100" => "Withdrawal succeeded",
    // "101" => "Failed to withdraw money",
    // "700" => "IEO order",
    // "701" => "Increase in currency transfer",
    // "702" => "Decrease in currency transfer",
    // "703" => "Handling charge for currency transfer",
    // "704" => "Lockdown mining",
    // "705" => "Mining income",
    // "706" => "The next singular cannot be less than",
    // "1000" => "Purchase dual currency financial management",
    // "1001" => "Dual currency financial settlement",
    // "707" => "Follow purchase - deduct margin",
    // "708" => "Follow the purchase - deduct handling charges",
    // "1002" => "Buy NFT",
    // "1003" => "Auction deduction deposit",
    // "1005" => "NFT auction order payment",
    // "1004" => "Auction refund deposit",
    // '32' => 'Breakout freeze',
    // '35' => 'Transaction fee',
    // '30' => 'Contract transactions deduct margin',
];