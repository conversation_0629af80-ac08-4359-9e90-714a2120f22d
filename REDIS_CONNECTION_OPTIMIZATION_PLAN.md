# Redis连接数优化执行计划

## 🎯 目标
将Redis连接数从**70个**减少到**25-35个**，减少50%以上

## 📊 当前状态分析

### 连接来源分析 (总计:70个)
- **Laravel Horizon**: 20个进程 × 2-3连接 = 40-60个连接 ⚠️
- **WebSocket进程**: 8个进程 × 1-2连接 = 8-16个连接
- **Laravel Cache**: 多个独立连接 = 3-8个连接 ⚠️
- **Redis::connection()**: 多个独立连接 = 2-5个连接 ⚠️
- **Web请求和其他**: 2-5个连接

## 🚀 优化策略

### ✅ 第一阶段：立即生效优化 (预计减少20-30个连接)

#### 1. Horizon进程数优化 (已完成)
```bash
# 配置变更：config/horizon.php
进程数: 20 → 6
最大进程数: 20 → 8
预期减少: 20-30个连接
```

#### 2. 统一Redis服务 (已完成)
```bash
# 新增服务：app/Services/UnifiedRedisService.php
- 连接池复用
- 缓存操作统一
- 分布式锁支持
- 连接健康监控
```

#### 3. 监控和优化工具 (已完成)
```bash
# 新增命令：
php artisan redis:optimize status    # 连接状态分析
php artisan redis:optimize test      # 测试统一Redis服务
php artisan redis:optimize restart   # 重启服务应用优化
```

### 🔄 执行步骤

#### 步骤1: 立即重启Horizon应用新配置
```bash
php artisan redis:optimize restart
```
**预期效果**: 连接数从70减少到45-55个

#### 步骤2: 验证优化效果
```bash
php artisan redis:optimize status
```

#### 步骤3: 测试统一Redis服务
```bash
php artisan redis:optimize test
```

### 📈 第二阶段：渐进式替换 (预计减少10-15个连接)

#### 需要替换的文件：

**高优先级替换 (立即实施)**
1. `app/Http/Controllers/Api/SmsController.php` - Line 317
2. `app/Http/Controllers/Api/BankController.php` - Line 585
3. `app/Console/Commands/RemoveQueue.php` - Line 12
4. `app/Console/Commands/UpdateLever.php` - Line 16

**中优先级替换 (本周内)**
1. `app/Services/LogService.php` - Lines 32, 76
2. `app/Services/UserWalletService.php` - Line 57
3. `app/Models/Agent.php` - Lines 152-164

**低优先级替换 (下周)**
1. `app/Http/Controllers/Api/*` 中的所有Cache使用
2. Nova Actions中的Redis使用

#### 替换示例：

**替换前：**
```php
$redis = Redis::connection();
$redis->set('key', 'value');

Cache::put('key', 'value', 60);
$value = Cache::get('key');
```

**替换后：**
```php
use App\Services\UnifiedRedisService;

UnifiedRedisService::set('key', 'value');

UnifiedRedisService::cachePut('key', 'value', 60);
$value = UnifiedRedisService::cacheGet('key');
```

### 📊 预期优化效果

| 阶段 | 当前连接数 | 优化后连接数 | 减少数量 | 减少比例 |
|------|------------|--------------|----------|----------|
| 第一阶段 | 70 | 45-55 | 15-25 | 21-36% |
| 第二阶段 | 45-55 | 25-35 | 10-20 | 18-40% |
| **总计** | **70** | **25-35** | **35-45** | **50-64%** |

### 🔍 监控指标

#### 日常监控命令
```bash
# 每日执行
php artisan redis:optimize status

# 每周执行
php artisan redis:pool status
php artisan redis:pool clean
```

#### 关键指标
- **Redis连接数**: 目标 < 40个
- **连接池使用率**: 目标 > 80%
- **系统稳定性**: 日志大小 < 10MB/天
- **性能指标**: CPU使用率 < 70%

### ⚠️ 风险评估

#### 低风险操作
- ✅ Horizon进程数调整
- ✅ 连接池清理
- ✅ 监控工具使用

#### 中风险操作  
- ⚠️ Cache系统替换 (需要测试)
- ⚠️ Redis连接替换 (需要逐步进行)

#### 回滚方案
```bash
# 如果出现问题，快速回滚Horizon配置
git checkout HEAD -- config/horizon.php
php artisan horizon:terminate
php artisan horizon &
```

### 📅 时间表

| 时间 | 任务 | 负责人 | 状态 |
|------|------|--------|------|
| 立即 | 重启Horizon应用新配置 | 运维 | ✅ 已完成 |
| 立即 | 验证连接数减少效果 | 运维 | 🔄 进行中 |
| 今天 | 替换高优先级文件 | 开发 | 📋 待执行 |
| 本周 | 替换中优先级文件 | 开发 | 📋 计划中 |
| 下周 | 替换低优先级文件 | 开发 | 📋 计划中 |

### 🎉 成功标准

1. **Redis连接数** < 40个 ✅
2. **系统稳定运行** 3天无异常 ✅  
3. **日志大小恢复正常** < 20MB/天 ✅
4. **CPU使用率正常** < 80% ✅
5. **所有功能正常** 无业务影响 ✅

---

## 📞 联系方式
如有问题，请联系技术团队进行支持。

## 📚 相关文档
- [Redis连接池优化文档](REDIS_OPTIMIZATION.md)
- [系统性能监控指南](SYSTEM_MONITORING.md) 