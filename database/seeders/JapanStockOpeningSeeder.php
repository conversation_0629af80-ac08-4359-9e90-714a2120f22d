<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class JapanStockOpeningSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 获取所有日本股票
        $japanStocks = DB::table('currency')
            ->where('data_source_type', 'stock-jp')
            ->where('is_display', 1)
            ->get();

        foreach ($japanStocks as $stock) {
            // 检查是否已存在开盘时间配置
            $existing = DB::table('currency_openings')
                ->where('currency_id', $stock->id)
                ->first();

            if (!$existing) {
                // 插入日本股市标准开盘时间
                DB::table('currency_openings')->insert([
                    'currency_id' => $stock->id,
                    'timezone' => 'Asia/Tokyo',
                    
                    // 周一到周五：上午 9:00-11:30，下午 12:30-15:00
                    'mon_begin' => '09:00:00',
                    'mon_end' => '11:30:00',
                    'mon_begin_2' => '12:30:00',
                    'mon_end_2' => '15:00:00',
                    
                    'tue_begin' => '09:00:00',
                    'tue_end' => '11:30:00',
                    'tue_begin_2' => '12:30:00',
                    'tue_end_2' => '15:00:00',
                    
                    'wed_begin' => '09:00:00',
                    'wed_end' => '11:30:00',
                    'wed_begin_2' => '12:30:00',
                    'wed_end_2' => '15:00:00',
                    
                    'thu_begin' => '09:00:00',
                    'thu_end' => '11:30:00',
                    'thu_begin_2' => '12:30:00',
                    'thu_end_2' => '15:00:00',
                    
                    'fin_begin' => '09:00:00',
                    'fin_end' => '11:30:00',
                    'fin_begin_2' => '12:30:00',
                    'fin_end_2' => '15:00:00',
                    
                    // 周六周日休市
                    'sat_begin' => null,
                    'sat_end' => null,
                    'sat_begin_2' => null,
                    'sat_end_2' => null,
                    
                    'sun_begin' => null,
                    'sun_end' => null,
                    'sun_begin_2' => null,
                    'sun_end_2' => null,
                    
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                echo "为股票 {$stock->name} (ID: {$stock->id}) 创建了开盘时间配置\n";
            } else {
                echo "股票 {$stock->name} (ID: {$stock->id}) 已存在开盘时间配置\n";
            }
        }
    }
}
