<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMultipleSessionsToCurrencyOpenings extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('currency_openings', function (Blueprint $table) {
            // 添加时区字段
            $table->string('timezone', 50)->default('UTC')->comment('时区');
            
            // 为每天添加第二个交易时段（支持中午休盘）
            $table->time('mon_begin_2')->nullable()->comment('周一第二时段开始');
            $table->time('mon_end_2')->nullable()->comment('周一第二时段结束');
            $table->time('tue_begin_2')->nullable()->comment('周二第二时段开始');
            $table->time('tue_end_2')->nullable()->comment('周二第二时段结束');
            $table->time('wed_begin_2')->nullable()->comment('周三第二时段开始');
            $table->time('wed_end_2')->nullable()->comment('周三第二时段结束');
            $table->time('thu_begin_2')->nullable()->comment('周四第二时段开始');
            $table->time('thu_end_2')->nullable()->comment('周四第二时段结束');
            $table->time('fin_begin_2')->nullable()->comment('周五第二时段开始');
            $table->time('fin_end_2')->nullable()->comment('周五第二时段结束');
            $table->time('sat_begin_2')->nullable()->comment('周六第二时段开始');
            $table->time('sat_end_2')->nullable()->comment('周六第二时段结束');
            $table->time('sun_begin_2')->nullable()->comment('周日第二时段开始');
            $table->time('sun_end_2')->nullable()->comment('周日第二时段结束');
            
            // 修改现有字段类型为time（如果还不是的话）
            $table->time('mon_begin')->nullable()->change();
            $table->time('mon_end')->nullable()->change();
            $table->time('tue_begin')->nullable()->change();
            $table->time('tue_end')->nullable()->change();
            $table->time('wed_begin')->nullable()->change();
            $table->time('wed_end')->nullable()->change();
            $table->time('thu_begin')->nullable()->change();
            $table->time('thu_end')->change();
            $table->time('fin_begin')->nullable()->change();
            $table->time('fin_end')->nullable()->change();
            $table->time('sat_begin')->nullable()->change();
            $table->time('sat_end')->nullable()->change();
            $table->time('sun_begin')->nullable()->change();
            $table->time('sun_end')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('currency_openings', function (Blueprint $table) {
            $table->dropColumn([
                'timezone',
                'mon_begin_2', 'mon_end_2',
                'tue_begin_2', 'tue_end_2', 
                'wed_begin_2', 'wed_end_2',
                'thu_begin_2', 'thu_end_2',
                'fin_begin_2', 'fin_end_2',
                'sat_begin_2', 'sat_end_2',
                'sun_begin_2', 'sun_end_2'
            ]);
        });
    }
}
