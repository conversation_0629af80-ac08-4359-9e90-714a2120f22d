# Redis 连接优化方案

## 🎯 当前问题
- Redis连接数过高(72个)，导致资源浪费
- 多种Redis使用方式混合，缺乏统一管理
- 缺少连接池和超时机制

## ✅ 已实施优化

### 1. 改进 RedisService.php
- ✅ 添加连接池机制
- ✅ 连接复用和自动清理
- ✅ 连接超时管理
- ✅ 连接健康检查

### 2. 添加监控命令
```bash
# 查看连接池状态
php artisan redis:pool status

# 清理过期连接
php artisan redis:pool clean

# 关闭所有连接
php artisan redis:pool close
```

### 3. 定时清理任务
- 每15分钟自动清理过期连接

## 🔧 环境变量优化配置

在 `.env` 文件中添加以下配置：

```env
# Redis基础配置
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_CLIENT=predis

# 连接池配置
REDIS_POOL_MAX_CONNECTIONS=5
REDIS_POOL_TIMEOUT=300
REDIS_POOL_READ_TIMEOUT=10

# Laravel Redis配置
REDIS_DB=0
REDIS_CACHE_DB=1
REDIS_SESSION_DB=2
REDIS_QUEUE_DB=3

# 队列配置优化
QUEUE_CONNECTION=redis
QUEUE_DRIVER=redis
HORIZON_TIMEOUT=300
```

## 📊 预期效果

### 优化前
- 连接数：72个
- 连接管理：无控制
- 资源利用：低效

### 优化后
- 连接数：预计减少到 15-25个
- 连接管理：自动清理和复用
- 资源利用：高效

### 具体改进
- 🔻 连接数减少 60-70%
- 🔻 内存使用减少
- 🔻 网络开销降低
- ⚡ 响应速度提升
- 🛡️ 稳定性增强

## 📈 连接数来源分析

### 主要连接来源
1. **WebSocket进程**: 8个进程 × 2-3个连接 = 16-24个
2. **Laravel Horizon**: 队列工作进程 = 5-10个
3. **Web请求**: 用户访问和API调用 = 5-15个
4. **定时任务**: Cron任务 = 2-5个
5. **缓存连接**: Laravel Cache系统 = 2-5个

### 优化策略
- **进程级连接复用**: 同一进程内复用连接
- **数据库级连接池**: 每个数据库最多5个连接
- **自动清理**: 超时连接自动回收
- **健康检查**: 定期检查连接有效性

## 🚀 进一步优化建议

### 1. Redis服务器配置
在 `redis.conf` 中添加：
```conf
# 最大客户端连接数
maxclients 100

# 客户端超时时间(秒)
timeout 300

# 启用TCP keepalive
tcp-keepalive 60

# 关闭不活跃连接
client-output-buffer-limit normal 0 0 0
```

### 2. Laravel Horizon优化
```php
// config/horizon.php
'environments' => [
    'production' => [
        'supervisor-1' => [
            'connection' => 'redis',
            'queue' => ['default', 'kline.all', 'esearch:market'],
            'balance' => 'simple',
            'processes' => 3, // 减少进程数
            'tries' => 3,
            'timeout' => 60,
        ],
    ],
],
```

### 3. 缓存优化
```php
// 使用单一连接实例
Cache::store('redis')->remember($key, $ttl, $callback);

// 批量操作
Redis::pipeline(function ($pipe) {
    $pipe->set('key1', 'value1');
    $pipe->set('key2', 'value2');
});
```

### 4. 监控脚本
创建监控脚本定期检查：
```bash
#!/bin/bash
# redis_monitor.sh
echo "=== Redis 连接监控 $(date) ==="
redis-cli info clients | grep connected_clients
php artisan redis:pool status
echo "================================"
```

## 📋 实施步骤

### 立即执行
1. ✅ 部署改进的 `RedisService.php`
2. ✅ 注册监控命令
3. ✅ 添加定时清理任务
4. 🔄 重启相关服务

### 监控验证
```bash
# 查看当前连接数
redis-cli info clients

# 查看连接池状态
php artisan redis:pool status

# 手动清理测试
php artisan redis:pool clean
```

### 持续优化
- 每天检查连接数变化
- 根据业务量调整连接池大小
- 优化队列配置减少连接数

## ⚠️ 注意事项

1. **渐进式部署**: 建议先在测试环境验证
2. **监控连接数**: 部署后密切观察连接数变化
3. **性能测试**: 确保优化不影响系统性能
4. **备份配置**: 保留原始配置以便回滚

## 🔍 故障排查

如果连接数仍然较高：
```bash
# 查看哪些进程在使用Redis
redis-cli client list | head -20

# 检查连接来源
netstat -an | grep :6379

# 查看进程Redis使用情况
lsof -i :6379
``` 